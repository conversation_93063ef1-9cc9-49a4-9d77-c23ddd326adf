'use client';

import React, { useState } from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  Stack,
  Image,
  Spinner,
  Field,
} from '@chakra-ui/react';
import { RadioGroup, Radio } from '@chakra-ui/react';
import { Alert } from '@/components/ui/alert';
import { MdError } from 'react-icons/md';
import { useQuery } from '@tanstack/react-query';
import { useTranslations, useLocale } from 'next-intl';
import { paymentService, type PaymentMethods } from '@/services';

interface PaymentMethodSelectorProps {
  currency: 'IDR' | 'USD';
  onMethodSelect: (method: string, type: string) => void;
  selectedMethod?: string;
  isLoading?: boolean;
}

const PaymentMethodSelector: React.FC<PaymentMethodSelectorProps> = ({
  currency,
  onMethodSelect,
  selectedMethod,
  isLoading = false,
}) => {
  const t = useTranslations('Payment');
  const locale = useLocale();
  const [selectedType, setSelectedType] = useState<string>('');
  const [selectedProvider, setSelectedProvider] = useState<string>('');

  // Fetch available payment methods
  const { data: paymentMethods, isLoading: methodsLoading } = useQuery<PaymentMethods>({
    queryKey: ['paymentMethods', currency],
    queryFn: () => paymentService.getPaymentMethods(currency),
  });

  const handleMethodChange = (type: string, provider?: string) => {
    setSelectedType(type);
    setSelectedProvider(provider || '');
    onMethodSelect(provider || type, type);
  };

  const getMethodIcon = (method: string) => {
    const icons: { [key: string]: string } = {
      invoice: '/icons/invoice.png',
      OVO: '/icons/ovo.png',
      DANA: '/icons/dana.png',
      LINKAJA: '/icons/linkaja.png',
      SHOPEEPAY: '/icons/shopeepay.png',
      BCA: '/icons/bca.png',
      BNI: '/icons/bni.png',
      BRI: '/icons/bri.png',
      MANDIRI: '/icons/mandiri.png',
      ALFAMART: '/icons/alfamart.png',
      INDOMARET: '/icons/indomaret.png',
    };
    return icons[method] || '/icons/default-payment.png';
  };

  if (methodsLoading) {
    return (
      <Box p={4} textAlign="center">
        <Spinner size="lg" />
        <Text mt={2}>{t('loadingMethods')}</Text>
      </Box>
    );
  }

  if (!paymentMethods) {
    return (
      <Alert status="error" title="Error">
        <MdError />
        {t('failedToLoadMethods')}
      </Alert>
    );
  }

  return (
    <Box bg="bg.panel" borderWidth="1px" borderRadius="lg" p={6}>
      <Text fontSize="lg" fontWeight="bold" mb={4}>
        {t('selectPaymentMethod')}
      </Text>

      <VStack gap={4} align="stretch">
        {/* Invoice Payment */}
        {paymentMethods.invoice && (
          <Box>
            <Text fontWeight="semibold" mb={2}>
              {t('invoicePayment')}
            </Text>
            <Box
              p={3}
              borderWidth="1px"
              borderColor={selectedType === 'invoice' ? 'blue.500' : 'border'}
              borderRadius="md"
              cursor="pointer"
              onClick={() => handleMethodChange('invoice')}
              bg={selectedType === 'invoice' ? 'blue.50' : 'transparent'}
            >
              <HStack>
                <Radio
                  checked={selectedType === 'invoice'}
                  onChange={() => handleMethodChange('invoice')}
                />
                <Image src={getMethodIcon('invoice')} alt="Invoice" boxSize="24px" />
                <Text>{t('invoiceDescription')}</Text>
              </HStack>
            </Box>
          </Box>
        )}

        {/* E-Wallet */}
        {paymentMethods.ewallet.length > 0 && (
          <Box>
            <Text fontWeight="semibold" mb={2}>
              {t('eWallet')}
            </Text>
            <Field>
              <RadioGroup 
                value={selectedProvider} 
                onValueChange={(details) => handleMethodChange('ewallet', details.value)}
              >
                <Stack gap={2}>
                  {paymentMethods.ewallet.map((wallet) => (
                    <Box
                      key={wallet}
                      p={3}
                      borderWidth="1px"
                      borderColor={selectedProvider === wallet ? 'blue.500' : 'border'}
                      borderRadius="md"
                      cursor="pointer"
                      onClick={() => handleMethodChange('ewallet', wallet)}
                      bg={selectedProvider === wallet ? 'blue.50' : 'transparent'}
                    >
                      <HStack>
                        <Radio value={wallet} />
                        <Image src={getMethodIcon(wallet)} alt={wallet} boxSize="24px" />
                        <Text>{wallet}</Text>
                      </HStack>
                    </Box>
                  ))}
                </Stack>
              </RadioGroup>
            </Field>
          </Box>
        )}

        {/* Virtual Account */}
        {paymentMethods.virtualAccount.length > 0 && (
          <Box>
            <Text fontWeight="semibold" mb={2}>
              {t('virtualAccount')}
            </Text>
            <Field>
              <RadioGroup 
                value={selectedProvider} 
                onValueChange={(details) => handleMethodChange('virtualAccount', details.value)}
              >
                <Stack gap={2}>
                  {paymentMethods.virtualAccount.map((bank) => (
                    <Box
                      key={bank}
                      p={3}
                      borderWidth="1px"
                      borderColor={selectedProvider === bank ? 'blue.500' : 'border'}
                      borderRadius="md"
                      cursor="pointer"
                      onClick={() => handleMethodChange('virtualAccount', bank)}
                      bg={selectedProvider === bank ? 'blue.50' : 'transparent'}
                    >
                      <HStack>
                        <Radio value={bank} />
                        <Image src={getMethodIcon(bank)} alt={bank} boxSize="24px" />
                        <Text>{bank}</Text>
                      </HStack>
                    </Box>
                  ))}
                </Stack>
              </RadioGroup>
            </Field>
          </Box>
        )}

        {/* Retail Outlet */}
        {paymentMethods.retailOutlet.length > 0 && (
          <Box>
            <Text fontWeight="semibold" mb={2}>
              {t('retailOutlet')}
            </Text>
            <Field>
              <RadioGroup 
                value={selectedProvider} 
                onValueChange={(details) => handleMethodChange('retailOutlet', details.value)}
              >
                <Stack gap={2}>
                  {paymentMethods.retailOutlet.map((outlet) => (
                    <Box
                      key={outlet}
                      p={3}
                      borderWidth="1px"
                      borderColor={selectedProvider === outlet ? 'blue.500' : 'border'}
                      borderRadius="md"
                      cursor="pointer"
                      onClick={() => handleMethodChange('retailOutlet', outlet)}
                      bg={selectedProvider === outlet ? 'blue.50' : 'transparent'}
                    >
                      <HStack>
                        <Radio value={outlet} />
                        <Image src={getMethodIcon(outlet)} alt={outlet} boxSize="24px" />
                        <Text>{outlet}</Text>
                      </HStack>
                    </Box>
                  ))}
                </Stack>
              </RadioGroup>
            </Field>
          </Box>
        )}
      </VStack>
    </Box>
  );
};

export default PaymentMethodSelector;
