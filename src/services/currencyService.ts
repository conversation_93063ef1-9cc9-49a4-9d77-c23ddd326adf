import { api } from './api';

export interface ExchangeRate {
  from: string;
  to: string;
  rate: number;
  timestamp: string;
}

export interface CurrencyConversion {
  originalAmount: number;
  originalCurrency: string;
  convertedAmount: number;
  convertedCurrency: string;
  exchangeRate: number;
  lastUpdated: string;
}

export interface SupportedCurrency {
  code: string;
  name: string;
  symbol: string;
}

export interface ProductPriceConversion {
  originalPrice: number;
  originalCurrency: string;
  convertedPrice: number;
  convertedCurrency: string;
  exchangeRate?: number;
  formattedPrice: string;
}

export interface CurrencyFormat {
  amount: number;
  currency: string;
  locale: string;
  formatted: string;
  symbol: string;
}

class CurrencyService {
  async getExchangeRate(from: string, to: string): Promise<ExchangeRate> {
    return api.get<ExchangeRate>('/currency/exchange-rate', {
      params: { from, to }
    });
  }

  async convertCurrency(
    amount: number,
    from: string,
    to: string
  ): Promise<CurrencyConversion> {
    return api.get<CurrencyConversion>('/currency/convert', {
      params: { amount, from, to }
    });
  }

  async getSupportedCurrencies(): Promise<SupportedCurrency[]> {
    return api.get<SupportedCurrency[]>('/currency/supported');
  }

  async getMultipleExchangeRates(
    base: string,
    targets: string[]
  ): Promise<{ base: string; rates: Record<string, number>; timestamp: string }> {
    return api.get<{ base: string; rates: Record<string, number>; timestamp: string }>(
      '/currency/rates',
      {
        params: {
          base,
          targets: targets.join(',')
        }
      }
    );
  }

  async convertProductPrice(
    priceUSD: number,
    locale: string
  ): Promise<ProductPriceConversion> {
    return api.get<ProductPriceConversion>('/currency/product-price', {
      params: { priceUSD, locale }
    });
  }

  async formatCurrency(
    amount: number,
    currency: string,
    locale?: string
  ): Promise<CurrencyFormat> {
    const params: any = { amount, currency };
    if (locale) params.locale = locale;

    return api.get<CurrencyFormat>('/currency/format', {
      params
    });
  }
}

export const currencyService = new CurrencyService();
