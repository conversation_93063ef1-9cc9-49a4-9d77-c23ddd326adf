import { api, ApiError } from './api';

export interface BidData {
  productId: string;
  amount: number;
  bidType?: 'manual' | 'auto';
}

export interface AutoBidData {
  productId: string;
  maxBudget: number;
  incrementStep: number;
}

export interface Bid {
  id: string;
  amount: number;
  isWinning: boolean;
  createdAt: string;
  bidder: {
    id: string;
    name: string;
  };
}

export interface BidHistory {
  bids: Bid[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  highestBid: number | null;
  currentWinner: {
    id: string;
    name: string;
    email: string;
  } | null;
}

export interface AutoBidSettings {
  id: string;
  productId: string;
  userId: string;
  maxBudget: number;
  incrementStep: number;
  currentBid: number | null;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface UserBidSummary {
  productId: string;
  product: {
    id: string;
    itemName: string;
    slug: string | null;
    currentBid: number | null;
    auctionEndDate: string | null;
    status: string;
    images: Array<{
      id: string;
      imageUrl: string;
      isMain: boolean;
    }>;
  };
  highestBid: number;
  totalBids: number;
  isWinning: boolean;
  lastBidTime: string;
  auctionStatus: 'active' | 'ended' | 'won' | 'lost';
}

export interface UserBidsResponse {
  bids: UserBidSummary[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

class BiddingService {
  async placeBid(productId: string, data: Omit<BidData, 'productId'>): Promise<any> {
    return api.post<any>(`/bidding/products/${productId}/bid`, {
      ...data,
      productId
    });
  }

  async setupAutoBid(productId: string, data: Omit<AutoBidData, 'productId'>): Promise<AutoBidSettings> {
    return api.post<AutoBidSettings>(`/bidding/products/${productId}/auto-bid`, {
      ...data,
      productId
    });
  }

  async getUserAutoBid(productId: string): Promise<AutoBidSettings | null> {
    try {
      return await api.get<AutoBidSettings>(`/bidding/products/${productId}/auto-bid`);
    } catch (error) {
      if (error instanceof ApiError && error.status === 404) {
        return null;
      }
      throw error;
    }
  }

  async deactivateAutoBid(productId: string): Promise<AutoBidSettings> {
    return api.delete<AutoBidSettings>(`/bidding/products/${productId}/auto-bid`);
  }

  async getBidHistory(
    productId: string,
    params?: { page?: number; limit?: number }
  ): Promise<BidHistory> {
    return api.get<BidHistory>(`/bidding/products/${productId}/history`, {
      params
    });
  }

  async getUserBids(params?: {
    page?: number;
    limit?: number;
    status?: 'active' | 'ended' | 'won' | 'lost';
    sortBy?: 'createdAt' | 'amount' | 'auctionEndDate';
    sortOrder?: 'asc' | 'desc';
  }): Promise<UserBidsResponse> {
    return api.get<UserBidsResponse>('/bidding/user-bids', {
      params
    });
  }

  async getBiddingSummary(): Promise<{
    activeBids: number;
    wonAuctions: number;
    totalBids: number;
  }> {
    return api.get<{
      activeBids: number;
      wonAuctions: number;
      totalBids: number;
    }>('/bidding/summary');
  }
}

export const biddingService = new BiddingService();
